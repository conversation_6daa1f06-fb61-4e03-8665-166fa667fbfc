/* Creative-minimalist Theme - Compiled CSS */

/* ===== NAVBAR STYLES ===== */
/* Navbar Styles */
.theme-creative-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.theme-creative-navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.theme-creative-navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  text-decoration: none;
  transition: color 0.3s ease;
}

.theme-creative-navbar-brand:hover {
  color: #3b82f6;
}

.theme-creative-navbar-nav {
  display: none;
  gap: 2rem;
}

@media (min-width: 768px) {
  .theme-creative-navbar-nav {
    display: flex;
  }
}

.theme-creative-navbar-link {
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  position: relative;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
}

.theme-creative-navbar-link:hover {
  color: #111827;
}

.theme-creative-navbar-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.theme-creative-navbar-link:hover::after {
  width: 100%;
}

.theme-creative-navbar-mobile-toggle {
  display: block;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.theme-creative-navbar-mobile-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
  background: transparent;
  border: none;
  cursor: pointer;
}

.theme-creative-navbar-mobile-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.theme-creative-navbar-mobile-icon {
  width: 1.5rem;
  height: 1.5rem;
}

@media (min-width: 768px) {
  .theme-creative-navbar-mobile-toggle {
    display: none;
  }
}

/* Mobile menu */
.theme-creative-navbar-mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.theme-creative-navbar-mobile-menu.active {
  display: block;
}

.theme-creative-navbar-mobile-nav {
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.theme-creative-navbar-mobile-link {
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
  transition: color 0.3s ease;
}

.theme-creative-navbar-mobile-link:hover {
  color: #111827;
}

.theme-creative-navbar-mobile-link:last-child {
  border-bottom: none;
}

/* Focus styles for accessibility */
.theme-creative-navbar-link:focus,
.theme-creative-navbar-mobile-link:focus,
.theme-creative-navbar-brand:focus {
  outline: none;
  color: #3b82f6;
}

.theme-creative-navbar-link:focus::after {
  width: 100%;
}

/* Print styles */
@media print {
  .theme-creative-navbar {
    display: none;
  }
}


/* ===== HERO STYLES ===== */
/* Hero Section */
.theme-creative-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  overflow: hidden;
  padding-top: 5rem; /* Add padding for navbar on all screens */
}

.theme-creative-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.theme-creative-hero-container {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.theme-creative-hero-layout {
  display: grid;
  gap: 2rem;
  align-items: center;
}

@media (min-width: 768px) {
  .theme-creative-hero-layout {
    gap: 3rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-layout {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

.theme-creative-hero-content {
  text-align: center;
  order: 2;
}

@media (min-width: 1024px) {
  .theme-creative-hero-content {
    text-align: left;
    order: 1;
  }
}

.theme-creative-hero-text {
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .theme-creative-hero-text {
    margin-bottom: 2rem;
  }
}

.theme-creative-hero-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
  line-height: 1.2;
}

@media (min-width: 480px) {
  .theme-creative-hero-title {
    font-size: 1.5rem;
  }
}

@media (min-width: 640px) {
  .theme-creative-hero-title {
    font-size: 2rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-title {
    font-size: 3rem;
  }
}

.theme-creative-hero-name {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.theme-creative-hero-profession {
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .theme-creative-hero-profession {
    margin-bottom: 2rem;
  }
}

.theme-creative-hero-profession-text {
  font-size: 1.125rem;
  color: #6b7280;
  font-weight: 500;
}

@media (min-width: 480px) {
  .theme-creative-hero-profession-text {
    font-size: 1.25rem;
  }
}

@media (min-width: 640px) {
  .theme-creative-hero-profession-text {
    font-size: 1.5rem;
  }
}

.theme-creative-hero-description {
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .theme-creative-hero-description {
    margin-bottom: 2rem;
  }
}

.theme-creative-hero-description-text {
  font-size: 1rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 auto;
  max-width: 600px;
}

@media (min-width: 480px) {
  .theme-creative-hero-description-text {
    font-size: 1.125rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-description-text {
    margin: 0;
    max-width: none;
  }
}

.theme-creative-hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

@media (min-width: 480px) {
  .theme-creative-hero-actions {
    gap: 1.5rem;
  }
}

@media (min-width: 640px) {
  .theme-creative-hero-actions {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-actions {
    justify-content: flex-start;
  }
}

.theme-creative-hero-action-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Button Styles */
.theme-creative-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.theme-creative-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.theme-creative-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.theme-creative-btn-secondary {
  background: rgba(255, 255, 255, 0.95);
  color: #4f46e5;
  border: 2px solid rgba(79, 70, 229, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.theme-creative-btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
  transition: left 0.5s ease;
}

.theme-creative-btn-secondary:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(79, 70, 229, 0.4);
  color: #3730a3;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
}

.theme-creative-btn-secondary:hover::before {
  left: 100%;
}

.theme-creative-btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.theme-creative-btn-outline:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.theme-creative-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.theme-creative-hero-upload-btn {
  cursor: pointer;
}

.theme-creative-hero-upload-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

/* Editable Field Styles */
/* Hero name gets special treatment to preserve gradient */
.theme-creative-hero-name[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  /* No background - preserve gradient text */
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  transition: all 0.3s ease;
  min-height: 1.5rem;
  z-index: 2;
}

/* Override shared CSS classes for hero name to preserve gradient */
.theme-creative-hero-name.editable-field-gradient,
.theme-creative-hero-name.editable-field-creative,
.theme-creative-hero-name.editable-field {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

/* Override EditableText component default styles for hero name */
.theme-creative-hero-name[contenteditable="true"] {
  /* Override Tailwind default styles from EditableText component */
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  border: 2px dashed rgba(102, 126, 234, 0.3) !important;
  border-radius: 0.375rem !important;
  padding: 0.5rem !important;
  margin: 0 !important;
  color: transparent !important; /* Ensure text color doesn't override gradient */
}

/* Ensure gradient is preserved on all states */
.theme-creative-hero-name[contenteditable="true"]:hover,
.theme-creative-hero-name[contenteditable="true"]:focus,
.theme-creative-hero-name[contenteditable="true"]:active {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  color: transparent !important;
}

/* Other hero editable fields get standard styling */
.theme-creative-hero-profession-text[contenteditable="true"],
.theme-creative-hero-description-text[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1.5rem;
}

/* Separate hover styles for hero name to preserve gradient */
.theme-creative-hero-name[contenteditable="true"]:hover,
.theme-creative-hero-name.editable-field-gradient:hover,
.theme-creative-hero-name.editable-field-creative:hover,
.theme-creative-hero-name.editable-field:hover {
  border-color: rgba(102, 126, 234, 0.5);
  /* Don't add background - preserve gradient text */
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.theme-creative-hero-profession-text[contenteditable="true"]:hover,
.theme-creative-hero-description-text[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

/* Separate focus styles for hero name to preserve gradient */
.theme-creative-hero-name[contenteditable="true"]:focus,
.theme-creative-hero-name.editable-field-gradient:focus,
.theme-creative-hero-name.editable-field-creative:focus,
.theme-creative-hero-name.editable-field:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  /* Don't add background - preserve gradient text */
  background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

.theme-creative-hero-profession-text[contenteditable="true"]:focus,
.theme-creative-hero-description-text[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.theme-creative-hero-name[contenteditable="true"]:empty:before,
.theme-creative-hero-profession-text[contenteditable="true"]:empty:before,
.theme-creative-hero-description-text[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
}

/* Hero Image Styles */
.theme-creative-hero-image-container {
  position: relative;
  order: 1;
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .theme-creative-hero-image-container {
    margin-bottom: 0;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero-image-container {
    order: 2;
    justify-content: flex-end;
  }
}

.theme-creative-hero-image-wrapper {
  position: relative;
  width: 16rem;
  height: 16rem;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  cursor: pointer;
}

@media (min-width: 480px) {
  .theme-creative-hero-image-wrapper {
    width: 18rem;
    height: 18rem;
  }
}

@media (min-width: 768px) {
  .theme-creative-hero-image-wrapper {
    width: 20rem;
    height: 20rem;
  }
}

.theme-creative-hero-image-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
}

.theme-creative-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.theme-creative-hero-upload-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  text-align: center;
}

.theme-creative-hero-upload-overlay:hover {
  opacity: 1;
}

.theme-creative-hero-upload-icon {
  width: 2rem;
  height: 2rem;
  margin-bottom: 0.5rem;
}

.theme-creative-hero-upload-loading {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-creative-hero-upload-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  color: white;
}

.theme-creative-hero-upload-spinner {
  width: 2rem;
  height: 2rem;
  color: white;
}

.theme-creative-hero-upload-text {
  font-weight: 500;
}

/* These styles are now handled by the unified editable field styles above */

/* Duplicate styles removed - now handled by unified editable field styles above */

/* Responsive hero spacing improvements */
@media (min-width: 480px) {
  .theme-creative-hero {
    padding-top: 5.5rem;
  }
}

@media (min-width: 768px) {
  .theme-creative-hero {
    padding-top: 6rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-hero {
    padding-top: 6.5rem;
  }
}

/* Mobile-specific hero improvements */
@media (max-width: 479px) {
  .theme-creative-hero {
    padding-top: 5rem;
  }

  .theme-creative-hero-layout {
    gap: 1.5rem;
    padding: 1rem 0;
  }

  .theme-creative-hero-title {
    font-size: 1.75rem;
  }

  .theme-creative-hero-profession-text {
    font-size: 1rem;
  }

  .theme-creative-hero-description-text {
    font-size: 0.95rem;
  }

  .theme-creative-hero-image-wrapper {
    width: 14rem;
    height: 14rem;
  }

  .theme-creative-hero-actions {
    gap: 0.5rem;
  }
}

/* Export-specific hero styles */
@media print {
  .theme-creative-hero {
    min-height: auto;
    padding: 2rem 0;
  }
}


/* ===== ABOUT STYLES ===== */
/* About Section */
.theme-creative-about {
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

@media (min-width: 768px) {
  .theme-creative-about {
    padding: 5rem 1rem;
  }
}

.theme-creative-about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e2e8f0' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.theme-creative-about-container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.theme-creative-about-header {
  margin-bottom: 3rem;
}

@media (min-width: 768px) {
  .theme-creative-about-header {
    margin-bottom: 4rem;
  }
}

.theme-creative-about-title {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  text-align: center;
  margin-bottom: 1rem;
}

@media (min-width: 480px) {
  .theme-creative-about-title {
    font-size: 2.5rem;
  }
}

@media (min-width: 640px) {
  .theme-creative-about-title {
    font-size: 3rem;
  }
}

.theme-creative-about-divider {
  width: 4rem;
  height: 4px;
  background: #3b82f6;
  margin: 0 auto;
  border-radius: 2px;
}

@media (min-width: 480px) {
  .theme-creative-about-divider {
    width: 5rem;
  }
}

.theme-creative-about-layout {
  display: grid;
  gap: 2rem;
  align-items: start;
}

@media (min-width: 768px) {
  .theme-creative-about-layout {
    gap: 3rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-about-layout {
    grid-template-columns: 1.2fr 0.8fr;
    gap: 4rem;
    align-items: start;
  }
}

.theme-creative-about-content {
  display: grid;
  gap: 2rem;
}

@media (min-width: 768px) {
  .theme-creative-about-content {
    gap: 3rem;
  }
}

.theme-creative-about-description,
.theme-creative-about-qualifications {
  background: white;
  padding: 1.5rem;
  border-radius: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
}

@media (min-width: 768px) {
  .theme-creative-about-description,
  .theme-creative-about-qualifications {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-about-description,
  .theme-creative-about-qualifications {
    padding: 2.5rem;
  }
}

.theme-creative-about-description:hover,
.theme-creative-about-qualifications:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #d1d5db;
}

.theme-creative-about-section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.75rem;
}

@media (min-width: 768px) {
  .theme-creative-about-section-title {
    font-size: 1.5rem;
  }
}

.theme-creative-about-section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 2rem;
  height: 2px;
  background: #3b82f6;
}

.theme-creative-about-text,
.theme-creative-about-qualifications-text {
  font-size: 1rem;
  color: #374151;
  line-height: 1.7;
  margin: 0;
}

@media (min-width: 768px) {
  .theme-creative-about-text,
  .theme-creative-about-qualifications-text {
    font-size: 1.125rem;
  }
}

.theme-creative-about-qualifications-text {
  white-space: pre-line;
}

/* Enhanced editable text styling for about section */
.theme-creative-about-text[contenteditable="true"],
.theme-creative-about-qualifications-text[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 4rem;
}

.theme-creative-about-text[contenteditable="true"]:hover,
.theme-creative-about-qualifications-text[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.theme-creative-about-text[contenteditable="true"]:focus,
.theme-creative-about-qualifications-text[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.theme-creative-about-text[contenteditable="true"]:empty:before,
.theme-creative-about-qualifications-text[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
}

.theme-creative-about-highlight {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Mobile-specific improvements */
@media (max-width: 479px) {
  .theme-creative-about {
    padding: 2rem 0.75rem;
  }

  .theme-creative-about-title {
    font-size: 1.75rem;
  }

  .theme-creative-about-header {
    margin-bottom: 2rem;
  }

  .theme-creative-about-layout {
    gap: 1.5rem;
  }

  .theme-creative-about-description,
  .theme-creative-about-qualifications {
    padding: 1.25rem;
    border-radius: 1rem;
  }

  .theme-creative-about-section-title {
    font-size: 1.125rem;
    margin-bottom: 1rem;
  }

  .theme-creative-about-text,
  .theme-creative-about-qualifications-text {
    font-size: 0.95rem;
    line-height: 1.7;
  }
}

/* Tablet improvements */
@media (min-width: 768px) and (max-width: 1023px) {
  .theme-creative-about-layout {
    grid-template-columns: 1fr;
    max-width: 700px;
    margin: 0 auto;
  }
}

/* Print styles for about section */
@media print {
  .theme-creative-about {
    padding: 2rem 0;
    background: white;
  }

  .theme-creative-about-description,
  .theme-creative-about-qualifications {
    background: white;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}


/* ===== EXPERIENCE STYLES ===== */
/* ===== FRESH EXPERIENCE SECTION DESIGN ===== */

/* Main Section */
.theme-creative-experience {
  padding: 4rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  position: relative;
}

@media (min-width: 768px) {
  .theme-creative-experience {
    padding: 6rem 2rem;
  }
}

/* Container */
.theme-creative-experience-content {
  max-width: 1000px;
  margin: 0 auto;
}

.theme-creative-experience-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 0.5rem;
  letter-spacing: -0.025em;
}

@media (min-width: 768px) {
  .theme-creative-experience-title {
    font-size: 3.5rem;
  }
}

.theme-creative-experience-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 400;
  margin-bottom: 2rem;
}

/* Decorative underline */
.theme-creative-experience-title::after {
  content: "";
  display: block;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  margin: 1rem auto 0;
  border-radius: 2px;
}

/* Timeline Container */
.theme-creative-experience-timeline {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Experience Item */
.theme-creative-experience-item {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-creative-experience-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
}

.theme-creative-experience-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@media (min-width: 768px) {
  .theme-creative-experience-item {
    padding: 2.5rem;
  }
}

/* Experience Card Content */
.theme-creative-experience-card {
  width: 100%;
}

/* Published Layout */
.theme-creative-experience-published-layout {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 0;
}

.theme-creative-experience-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .theme-creative-experience-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
  }
}

.theme-creative-experience-title-section {
  flex: 1;
  min-width: 0;
  gap: 1rem;
}

.theme-creative-experience-role-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.theme-creative-experience-icon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
  flex-shrink: 0;
}

.theme-creative-experience-role {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
  line-height: 1.3;
}

@media (min-width: 768px) {
  .theme-creative-experience-role {
    font-size: 1.75rem;
  }
}

.theme-creative-experience-company-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.theme-creative-experience-company {
  font-size: 1.125rem;
  font-weight: 600;
  color: #3b82f6;
  margin: 0;
}

/* Meta Section (Duration & Location) */
.theme-creative-experience-meta-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-start;
}

@media (min-width: 768px) {
  .theme-creative-experience-meta-section {
    align-items: flex-end;
  }
}

.theme-creative-experience-duration-badge {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.theme-creative-experience-location-badge {
  color: #64748b;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.theme-creative-experience-meta-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Description */
.theme-creative-experience-description {
  color: #475569;
  line-height: 1.6;
  font-size: 1rem;
  margin: 0;
}

/* ===== EDITOR LAYOUT STYLES ===== */

.theme-creative-experience-editor-layout {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Editor Grid Rows */
.theme-creative-experience-editor-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .theme-creative-experience-editor-row {
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }
}

/* Editor Fields */
.theme-creative-experience-editor-field {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-height: 48px;
}

.theme-creative-experience-field-icon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
  flex-shrink: 0;
}

/* Base styling for experience field inputs (published view) */
.theme-creative-experience-field-input {
  flex: 1;
  font-size: 1rem;
  color: #1f2937;
  font-family: inherit;
  line-height: 1.5;
  min-height: 24px;
}

/* Editable Input Styling (only when contenteditable) */
.theme-creative-experience-field-input[contenteditable="true"] {
  background: rgba(59, 130, 246, 0.05);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 0.75rem;
  outline: none;
  transition: all 0.3s ease;
}

.theme-creative-experience-field-input[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.theme-creative-experience-field-input[contenteditable="true"]:focus {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.theme-creative-experience-field-input[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
}

/* Description Field */
.theme-creative-experience-editor-description {
  margin: 0;
}

.theme-creative-experience-editor-description
  .theme-creative-experience-field-input[contenteditable="true"] {
  min-height: 60px;
  resize: vertical;
  padding: 1rem;
}

/* Action Buttons */
.theme-creative-experience-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.theme-creative-experience-delete-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.theme-creative-experience-delete-btn:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.theme-creative-experience-delete-icon {
  width: 18px;
  height: 18px;
}

/* Add Experience Button */
.theme-creative-experience-add-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.theme-creative-experience-add-btn {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.theme-creative-experience-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.theme-creative-experience-add-icon {
  width: 20px;
  height: 20px;
}

/* Empty State */
.theme-creative-experience-empty {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.theme-creative-experience-empty p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 767px) {
  .theme-creative-experience {
    padding: 3rem 1rem;
  }

  .theme-creative-experience-title {
    font-size: 2rem;
  }

  .theme-creative-experience-item {
    padding: 1.5rem;
  }

  .theme-creative-experience-role {
    font-size: 1.25rem;
  }

  .theme-creative-experience-header {
    gap: 1.5rem;
  }

  .theme-creative-experience-meta-section {
    align-items: flex-start;
  }

  .theme-creative-experience-duration-badge {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .theme-creative-experience-title {
    font-size: 1.75rem;
  }

  .theme-creative-experience-item {
    padding: 1rem;
  }

  .theme-creative-experience-role-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .theme-creative-experience-company-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Animation for smooth appearance */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.theme-creative-experience-item {
  animation: fadeInUp 0.6s ease forwards;
}

.theme-creative-experience-item:nth-child(2) {
  animation-delay: 0.1s;
}

.theme-creative-experience-item:nth-child(3) {
  animation-delay: 0.2s;
}

.theme-creative-experience-item:nth-child(4) {
  animation-delay: 0.3s;
}


/* ===== SKILLS STYLES ===== */
/* Creative Skills Section */
.skills-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.skills-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.skills-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.skills-header {
  text-align: center;
  margin-bottom: 4rem;
}

.skills-title {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.skills-subtitle {
  font-size: 1.25rem;
  color: #64748b;
  font-weight: 400;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.skills-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* Skills Badges Grid */
.skills-badges-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  align-items: flex-start;
}

/* Individual Skill Badge */
.skill-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 2px solid transparent;
  border-radius: 2rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.08),
    0 2px 6px rgba(0, 0, 0, 0.04);
  cursor: default;
  overflow: hidden;
}

.skill-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(102, 126, 234, 0.1) 0%, 
    rgba(118, 75, 162, 0.1) 50%,
    rgba(255, 119, 198, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.skill-badge:hover::before {
  opacity: 1;
}

.skill-badge:hover {
  transform: translateY(-3px) scale(1.05);
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 
    0 10px 30px rgba(102, 126, 234, 0.2),
    0 4px 15px rgba(0, 0, 0, 0.1);
}

.skill-badge-text {
  position: relative;
  z-index: 1;
}

/* Editing Mode Styles */
.skill-badge-edit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.skill-badge-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  padding: 0;
  min-width: 80px;
}

.skill-badge-input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.skill-badge-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.skill-badge-delete:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.skill-badge-delete-icon {
  width: 0.75rem;
  height: 0.75rem;
}

/* Editing state styling */
.skill-badge:has(.skill-badge-edit) {
  background: rgba(102, 126, 234, 0.05);
  border-color: rgba(102, 126, 234, 0.3);
  border-style: dashed;
}

.skill-badge:has(.skill-badge-edit):hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
}

/* Add Button */
.skills-add-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.skills-add-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.skills-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.skills-add-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .skills-section {
    padding: 3rem 0;
  }
  
  .skills-container {
    padding: 0 1rem;
  }
  
  .skills-title {
    font-size: 2rem;
  }
  
  .skills-subtitle {
    font-size: 1rem;
  }
  
  .skills-badges-grid {
    gap: 0.75rem;
  }
  
  .skill-badge {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}


/* ===== PROJECTS STYLES ===== */
/* Modern Projects Section */
.projects-section {
  padding: 5rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.projects-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(99, 102, 241, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

@media (min-width: 768px) {
  .projects-section {
    padding: 6rem 1rem;
  }
}

.projects-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  padding: 1rem;
}

.projects-header {
  text-align: center;
  margin-bottom: 4rem;
}

@media (min-width: 768px) {
  .projects-header {
    margin-bottom: 5rem;
  }
}

.projects-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

@media (min-width: 640px) {
  .projects-title {
    font-size: 3rem;
  }
}

@media (min-width: 768px) {
  .projects-title {
    font-size: 3.5rem;
  }
}

.projects-title::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 50%;
  transform: translateX(-50%);
  width: 5rem;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

@media (min-width: 480px) {
  .projects-title::after {
    width: 6rem;
  }
}

.projects-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 500;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .projects-subtitle {
    font-size: 1.25rem;
  }
}

.projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .projects-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
  }
}

/* Project Card */
.project-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.25rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(203, 213, 225, 0.8);
}

/* Project Image */
.project-image-container {
  position: relative;
  width: 100%;
  height: 240px;
  overflow: hidden;
  background: #f1f5f9;
}

@media (min-width: 768px) {
  .project-image-container {
    height: 260px;
  }
}

.project-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-card:hover .project-image {
  transform: scale(1.05);
}

/* Upload Overlay */
.project-upload-overlay {
  position: absolute;
  inset: 0;
  background: rgba(15, 23, 42, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  font-weight: 600;
  cursor: pointer;
  z-index: 10;
}

/* Show upload overlay when hovering or when uploading */
.project-upload-overlay:hover,
.project-upload-overlay.uploading {
  opacity: 1;
}

.project-upload-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 20;
}

.project-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  text-align: center;
}

.project-upload-icon {
  width: 2rem;
  height: 2rem;
  color: white;
  opacity: 0.9;
}

.project-upload-text {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  opacity: 0.9;
}

.project-upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.project-upload-spinner {
  width: 2rem;
  height: 2rem;
  color: white;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Project Overlay */
.project-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 60%);
  display: flex;
  align-items: flex-end;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-overlay-content {
  padding: 1.5rem;
  width: 100%;
}

.project-overlay-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.project-overlay-links {
  display: flex;
  gap: 0.75rem;
}

.project-overlay-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.project-overlay-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.project-overlay-link-icon {
  width: 1.125rem;
  height: 1.125rem;
}

/* Project Content */
.project-content {
  padding: 1.75rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .project-content {
    padding: 2rem;
  }
}

.project-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.project-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex: 1;
  font-size: 0.95rem;
}

/* Editable Field Styles */
.project-title[contenteditable="true"],
.project-description[contenteditable="true"],
.project-url-field[contenteditable="true"] {
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.05);
  transition: all 0.3s ease;
  min-height: 2rem;
}

.project-title[contenteditable="true"]:hover,
.project-description[contenteditable="true"]:hover,
.project-url-field[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.08);
}

.project-title[contenteditable="true"]:focus,
.project-description[contenteditable="true"]:focus,
.project-url-field[contenteditable="true"]:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.project-title[contenteditable="true"]:empty:before,
.project-description[contenteditable="true"]:empty:before,
.project-url-field[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #94a3b8;
  font-style: italic;
}

.project-edit-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.project-url-field {
  font-size: 0.875rem;
  color: #64748b;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

.project-remove-btn {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
}

.project-remove-btn:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.project-remove-icon {
  width: 0.875rem;
  height: 0.875rem;
}

/* Add Project Card */
.project-add-card {
  background: rgba(255, 255, 255, 0.6);
  border: 2px dashed #cbd5e1;
  border-radius: 1.25rem;
  padding: 3rem 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 300px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.project-add-card:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.1);
}

.project-add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.project-add-icon {
  width: 3rem;
  height: 3rem;
  color: #64748b;
  transition: all 0.3s ease;
}

.project-add-card:hover .project-add-icon {
  color: #3b82f6;
  transform: scale(1.1);
}

.project-add-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: #64748b;
  transition: all 0.3s ease;
}

.project-add-card:hover .project-add-text {
  color: #3b82f6;
}

/* Mobile-specific improvements */
@media (max-width: 479px) {
  .projects-section {
    padding: 3rem 0.75rem;
  }

  .projects-title {
    font-size: 2rem;
  }

  .projects-header {
    margin-bottom: 2.5rem;
  }

  .projects-grid {
    gap: 1.5rem;
  }

  .project-image-container {
    height: 200px;
  }

  .project-content {
    padding: 1.25rem;
  }

  .project-add-card {
    padding: 2rem 1rem;
    min-height: 200px;
  }

  .project-add-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .project-add-text {
    font-size: 1rem;
  }
}

/* Print styles */
@media print {
  .projects-section {
    background: white;
  }

  .project-card {
    break-inside: avoid;
    margin-bottom: 1rem;
    background: white;
    border: 1px solid #e2e8f0;
  }

  .project-upload-overlay,
  .project-overlay,
  .project-edit-fields,
  .project-add-card {
    display: none;
  }
}


/* ===== CONTACT STYLES ===== */
/* Modern Contact Section */
.contact-section {
  padding: 5rem 1rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #ffffff 100%);
  position: relative;
  overflow: hidden;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.04) 0%, transparent 50%),
              radial-gradient(circle at 75% 25%, rgba(99, 102, 241, 0.04) 0%, transparent 50%);
  pointer-events: none;
}

@media (min-width: 768px) {
  .contact-section {
    padding: 6rem 1rem;
  }
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  padding: 1rem;
}

.contact-header {
  text-align: center;
  margin-bottom: 4rem;
}

@media (min-width: 768px) {
  .contact-header {
    margin-bottom: 5rem;
  }
}

.contact-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1rem;
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

@media (min-width: 640px) {
  .contact-title {
    font-size: 3rem;
  }
}

@media (min-width: 768px) {
  .contact-title {
    font-size: 3.5rem;
  }
}

.contact-title::after {
  content: '';
  position: absolute;
  bottom: -0.75rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

@media (min-width: 480px) {
  .contact-title::after {
    width: 5rem;
  }
}

.contact-subtitle {
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 500;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .contact-subtitle {
    font-size: 1.25rem;
  }
}

.contact-content {
  display: grid;
  gap: 3rem;
}

@media (min-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
  }
}

/* Contact Info Section */
.contact-info {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.25rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

@media (min-width: 768px) {
  .contact-info {
    padding: 2.5rem;
  }
}

.contact-info-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.contact-info-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 1.5px;
}

.contact-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.contact-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
}

.contact-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.75rem;
  flex-shrink: 0;
}

.contact-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}

.contact-item-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.contact-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
}

.contact-value {
  font-size: 1rem;
  font-weight: 500;
  color: #1e293b;
  word-break: break-word;
}

.contact-value-link {
  font-size: 1rem;
  font-weight: 500;
  color: #3b82f6;
  text-decoration: none;
  transition: color 0.2s ease;
  word-break: break-word;
}

.contact-value-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* Contact Item Colors */
.contact-email .contact-item-icon {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.contact-phone .contact-item-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.contact-location .contact-item-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.contact-email {
  border-left-color: #3b82f6;
}

.contact-phone {
  border-left-color: #10b981;
}

.contact-location {
  border-left-color: #f59e0b;
}

/* Social Section */
.social-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.25rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

@media (min-width: 768px) {
  .social-section {
    padding: 2.5rem;
  }
}

.social-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.social-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1);
  border-radius: 1.5px;
}

.social-grid {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.social-item {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.social-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
}

.social-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  color: #1e293b;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: rgba(59, 130, 246, 0.05);
}

.social-edit {
  padding: 1.25rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.social-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.75rem;
  flex-shrink: 0;
}

.social-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}

.social-edit-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.social-label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #64748b;
}

/* Social Item Colors */
.social-github .social-icon-wrapper {
  background: linear-gradient(135deg, #1f2937, #111827);
}

.social-linkedin .social-icon-wrapper {
  background: linear-gradient(135deg, #0077b5, #005885);
}

.social-twitter .social-icon-wrapper {
  background: linear-gradient(135deg, #1da1f2, #0d8bd9);
}

.social-github {
  border-left-color: #1f2937;
}

.social-linkedin {
  border-left-color: #0077b5;
}

.social-twitter {
  border-left-color: #1da1f2;
}

/* Editable Fields */
.contact-value-editable[contenteditable="true"],
.social-url-editable[contenteditable="true"] {
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.05);
  transition: all 0.3s ease;
  min-height: 2rem;
  font-size: 0.95rem;
  color: #1e293b;
  word-break: break-word;
}

.contact-value-editable[contenteditable="true"]:hover,
.social-url-editable[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.08);
}

.contact-value-editable[contenteditable="true"]:focus,
.social-url-editable[contenteditable="true"]:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.contact-value-editable[contenteditable="true"]:empty:before,
.social-url-editable[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #94a3b8;
  font-style: italic;
}

/* Mobile-specific improvements */
@media (max-width: 479px) {
  .contact-section {
    padding: 3rem 0.75rem;
  }

  .contact-title {
    font-size: 2rem;
  }

  .contact-header {
    margin-bottom: 2.5rem;
  }

  .contact-content {
    gap: 2rem;
  }

  .contact-info,
  .social-section {
    padding: 1.5rem;
  }

  .contact-item,
  .social-link,
  .social-edit {
    padding: 1rem;
  }

  .contact-item-icon,
  .social-icon-wrapper {
    width: 2rem;
    height: 2rem;
  }

  .contact-icon,
  .social-icon {
    width: 1rem;
    height: 1rem;
  }
}

/* Print styles */
@media print {
  .contact-section {
    background: white;
  }

  .contact-info,
  .social-section {
    background: white;
    border: 1px solid #e2e8f0;
    box-shadow: none;
  }

  .contact-item,
  .social-item {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e2e8f0;
  }

  .social-url-editable,
  .contact-value-editable {
    display: none;
  }
}


/* ===== FOOTER STYLES ===== */
/* Modern Footer Section */
.footer-section {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: #e2e8f0;
  position: relative;
  overflow: hidden;
}

.footer-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 20%, rgba(99, 102, 241, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.footer-border {
  height: 4px;
  background: linear-gradient(90deg, transparent 0%, #3b82f6 20%, #6366f1 50%, #8b5cf6 80%, transparent 100%);
  margin-bottom: 3rem;
  border-radius: 2px;
}

.footer-content {
  padding: 0 1rem 3rem;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

@media (min-width: 768px) {
  .footer-content {
    padding: 0 1rem 4rem;
  }
}

.footer-main {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
  align-items: center;
  text-align: center;
}

@media (min-width: 768px) {
  .footer-main {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    text-align: left;
  }
}

.footer-brand {
  flex: 1;
  max-width: 600px;
}

.footer-name {
  font-size: 2rem;
  font-weight: 800;
  color: white;
  margin-bottom: 0.75rem;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

@media (min-width: 768px) {
  .footer-name {
    font-size: 2.25rem;
  }
}

.footer-tagline {
  color: #94a3b8;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-weight: 400;
}

@media (min-width: 768px) {
  .footer-tagline {
    font-size: 1.125rem;
  }
}

/* Footer Stats */
.footer-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
}

@media (min-width: 768px) {
  .footer-stats {
    justify-content: flex-start;
  }
}

.footer-stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.footer-stat:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.footer-stat-icon {
  width: 1rem;
  height: 1rem;
  color: #60a5fa;
}

.footer-stat-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #cbd5e1;
}

/* Footer Actions */
.footer-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-scroll-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  color: #60a5fa;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  text-decoration: none;
}

.footer-scroll-top:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.2);
}

.footer-scroll-icon {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s ease;
}

.footer-scroll-top:hover .footer-scroll-icon {
  transform: translateY(-2px);
}

.footer-scroll-text {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

@media (min-width: 768px) {
  .footer-bottom {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: center;
}

@media (min-width: 768px) {
  .footer-info {
    align-items: flex-start;
  }
}

.footer-copyright {
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 400;
}

.footer-made-with {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.footer-made-text {
  font-weight: 400;
}

.footer-heart {
  width: 1rem;
  height: 1rem;
  color: #ef4444;
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 50%, 100% {
    transform: scale(1);
  }
  25%, 75% {
    transform: scale(1.2);
  }
}

.footer-sparkles {
  width: 1rem;
  height: 1rem;
  color: #fbbf24;
  animation: sparkle 3s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% {
    transform: rotate(0deg) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    opacity: 1;
  }
}

/* Footer Attribution */
.footer-attribution {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .footer-attribution {
    align-items: flex-end;
  }
}

.footer-powered {
  color: #64748b;
  font-weight: 400;
}

.footer-brand-name {
  color: #3b82f6;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.footer-brand-name:hover {
  background: linear-gradient(135deg, #2563eb, #4f46e5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Mobile-specific improvements */
@media (max-width: 479px) {
  .footer-content {
    padding: 0 0.75rem 2rem;
  }

  .footer-border {
    margin-bottom: 2rem;
  }

  .footer-name {
    font-size: 1.5rem;
  }

  .footer-tagline {
    font-size: 0.9rem;
  }

  .footer-stats {
    gap: 1rem;
  }

  .footer-stat {
    padding: 0.375rem 0.75rem;
  }

  .footer-stat-text {
    font-size: 0.8rem;
  }

  .footer-scroll-top {
    padding: 0.75rem;
  }

  .footer-scroll-icon {
    width: 1rem;
    height: 1rem;
  }

  .footer-scroll-text {
    font-size: 0.7rem;
  }

  .footer-bottom {
    gap: 1rem;
    padding-top: 1.5rem;
  }

  .footer-copyright,
  .footer-made-with,
  .footer-attribution {
    font-size: 0.8rem;
  }
}

/* Print styles */
@media print {
  .footer-section {
    background: white;
    color: #1f2937;
  }

  .footer-section::before {
    display: none;
  }

  .footer-border {
    background: #e5e7eb;
  }

  .footer-name {
    color: #1f2937;
    background: none;
    -webkit-text-fill-color: #1f2937;
  }

  .footer-brand-name {
    color: #3b82f6;
    background: none;
    -webkit-text-fill-color: #3b82f6;
  }

  .footer-scroll-top,
  .footer-stats {
    display: none;
  }

  .footer-main {
    align-items: center;
    text-align: center;
  }

  .footer-bottom {
    justify-content: center;
    text-align: center;
  }

  .footer-info {
    align-items: center;
  }
}


/* ===== BASE STYLES ===== */
/* Base styles and reset */
.theme-creative-root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #111827;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: white;
  overflow-x: hidden;
}

.theme-creative-root *,
.theme-creative-root *::before,
.theme-creative-root *::after {
  box-sizing: inherit;
}

/* Container utilities */
.theme-creative-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .theme-creative-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-creative-container {
    padding: 0 2rem;
  }
}

/* Common Button Styles */
.theme-creative-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 1rem;
}

.theme-creative-btn-primary {
  background: #3b82f6;
  color: white;
}

.theme-creative-btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.theme-creative-btn-secondary {
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
}

.theme-creative-btn-secondary:hover {
  background: #3b82f6;
  color: white;
}

.theme-creative-btn-danger {
  background: #ef4444;
  color: white;
}

.theme-creative-btn-danger:hover {
  background: #dc2626;
}

/* Common Form Styles */
.theme-creative-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.theme-creative-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.theme-creative-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  resize: vertical;
  min-height: 4rem;
}

.theme-creative-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

/* Common Card Styles */
.theme-creative-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.theme-creative-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #d1d5db;
}

/* Utility Classes */
.theme-creative-text-center {
  text-align: center;
}

.theme-creative-text-left {
  text-align: left;
}

.theme-creative-text-right {
  text-align: right;
}

.theme-creative-hidden {
  display: none;
}

.theme-creative-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Loading Spinner */
.theme-creative-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Utilities */
@media (max-width: 479px) {
  .theme-creative-hide-mobile {
    display: none;
  }
}

@media (min-width: 480px) {
  .theme-creative-show-mobile {
    display: none;
  }
}

@media (max-width: 767px) {
  .theme-creative-hide-tablet {
    display: none;
  }
}

@media (min-width: 768px) {
  .theme-creative-show-tablet {
    display: none;
  }
}

@media (max-width: 1023px) {
  .theme-creative-hide-desktop {
    display: none;
  }
}

@media (min-width: 1024px) {
  .theme-creative-show-desktop {
    display: none;
  }
}

/* Print Styles */
@media print {
  .theme-creative-no-print {
    display: none;
  }

  .theme-creative-root {
    background: white;
    color: black;
  }

  .theme-creative-card {
    box-shadow: none;
    border: 1px solid #e5e7eb;
    break-inside: avoid;
    margin-bottom: 1rem;
  }
}

/* Accessibility Improvements */
.theme-creative-focus-visible:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection Styles */
::selection {
  background: rgba(59, 130, 246, 0.2);
  color: #1f2937;
}

::-moz-selection {
  background: rgba(59, 130, 246, 0.2);
  color: #1f2937;
}


