/* Modern Theme - Compiled CSS */

/* ===== NAVBAR STYLES ===== */
/* Modern Theme - Navbar Component */

.theme-modern-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.theme-modern-navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.theme-modern-navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  text-decoration: none;
  transition: color 0.3s ease;
}

.theme-modern-navbar-brand:hover {
  color: #3b82f6;
}

.theme-modern-navbar-nav {
  display: none;
  gap: 2rem;
}

@media (min-width: 768px) {
  .theme-modern-navbar-nav {
    display: flex;
  }
}

.theme-modern-navbar-link {
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.theme-modern-navbar-link:hover {
  color: #1f2937;
}

.theme-modern-navbar-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.theme-modern-navbar-link:hover::after {
  width: 100%;
}

.theme-modern-navbar-mobile-toggle {
  display: block;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: #1f2937;
}

.theme-modern-navbar-mobile-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: #1f2937;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.theme-modern-navbar-mobile-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.theme-modern-navbar-mobile-icon {
  width: 1.5rem;
  height: 1.5rem;
}

@media (min-width: 768px) {
  .theme-modern-navbar-mobile-toggle {
    display: none;
  }
}

/* Mobile menu */
.theme-modern-navbar-mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.theme-modern-navbar-mobile-menu.active {
  display: block;
}

.theme-modern-navbar-mobile-nav {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
}

.theme-modern-navbar-mobile-link {
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
}

.theme-modern-navbar-mobile-link:hover {
  color: #1f2937;
}

/* Focus styles for navbar links */
.theme-modern-navbar-link:focus,
.theme-modern-navbar-mobile-link:focus,
.theme-modern-navbar-brand:focus {
  outline: none;
  color: #3b82f6;
}

.theme-modern-navbar-link:focus::after {
  width: 100%;
}


/* ===== HERO STYLES ===== */
/* Modern Theme - Hero Component */

.theme-modern-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.theme-modern-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.theme-modern-hero-container {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.theme-modern-hero-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  line-height: 1.1;
}

@media (min-width: 640px) {
  .theme-modern-hero-title {
    font-size: 4rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-title {
    font-size: 5rem;
  }
}

.theme-modern-hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .theme-modern-hero-subtitle {
    font-size: 1.5rem;
  }
}

.theme-modern-hero-cta {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.theme-modern-hero-cta:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.theme-modern-hero-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
  min-height: 100vh;
  justify-content: center;
  padding: 5rem 0;
}

@media (min-width: 1024px) {
  .theme-modern-hero-layout {
    flex-direction: row;
    gap: 5rem;
  }
}

.theme-modern-hero-content {
  text-align: center;
  max-width: 600px;
}

@media (min-width: 1024px) {
  .theme-modern-hero-content {
    text-align: left;
  }
}

.theme-modern-hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

@media (min-width: 640px) {
  .theme-modern-hero-actions {
    flex-direction: row;
    justify-content: center;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-actions {
    justify-content: flex-start;
  }
}

.theme-modern-hero-action-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.theme-modern-hero-upload-btn {
  cursor: pointer;
}

.theme-modern-hero-upload-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

/* Hero Image Styles */
.theme-modern-hero-image-container {
  position: relative;
  flex-shrink: 0;
}

.theme-modern-hero-image-wrapper {
  position: relative;
  width: 16rem;
  height: 16rem;
}

@media (min-width: 1024px) {
  .theme-modern-hero-image-wrapper {
    width: 20rem;
    height: 20rem;
  }
}

.theme-modern-hero-image-wrapper::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, #8b5cf6, #ec4899, #06b6d4);
  border-radius: 50%;
  filter: blur(1rem);
  opacity: 0.6;
  animation: spin 10s linear infinite;
}

.theme-modern-hero-image-wrapper:hover::before {
  opacity: 0.8;
}

.theme-modern-hero-image-wrapper::after {
  content: '';
  position: absolute;
  inset: 0.5rem;
  background: #111827;
  border-radius: 50%;
  z-index: 1;
}

.theme-modern-hero-image {
  position: absolute;
  inset: 1rem;
  width: calc(100% - 2rem);
  height: calc(100% - 2rem);
  border-radius: 50%;
  object-fit: cover;
  z-index: 10;
}

.theme-modern-hero-upload-overlay {
  position: absolute;
  inset: 1rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  z-index: 20;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.theme-modern-hero-image-wrapper:hover .theme-modern-hero-upload-overlay {
  opacity: 1;
}

.theme-modern-hero-upload-icon {
  width: 2rem;
  height: 2rem;
  margin-bottom: 0.5rem;
}

.theme-modern-hero-upload-loading {
  position: absolute;
  inset: 1rem;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 30;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.theme-modern-hero-upload-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: white;
}

.theme-modern-hero-upload-spinner {
  width: 4rem;
  height: 4rem;
  color: #8b5cf6;
  animation: spin 1s linear infinite;
}

.theme-modern-hero-upload-text {
  font-size: 1.125rem;
  font-weight: 600;
  background: linear-gradient(90deg, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}


/* ===== ABOUT STYLES ===== */
/* Modern Theme - About Component */

.theme-modern-about {
  padding: 5rem 1rem;
  background: #f9fafb;
}

.theme-modern-about-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  gap: 3rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .theme-modern-about-container {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

.theme-modern-about-content {
  text-align: center;
}

@media (min-width: 1024px) {
  .theme-modern-about-content {
    text-align: left;
  }
}

.theme-modern-about-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

@media (min-width: 640px) {
  .theme-modern-about-title {
    font-size: 3rem;
  }
}

.theme-modern-about-text {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.theme-modern-about-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}


/* ===== EXPERIENCE STYLES ===== */
/* Modern Theme - Experience Component */

.theme-modern-experience {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.theme-modern-experience-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .theme-modern-experience-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-experience-container {
    padding: 0 2rem;
  }
}

.theme-modern-experience-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #1e293b;
  position: relative;
}

.theme-modern-experience-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

.theme-modern-experience-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.theme-modern-experience-timeline::before {
  content: '';
  position: absolute;
  left: 2rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #3b82f6, #8b5cf6);
  border-radius: 1px;
}

@media (min-width: 768px) {
  .theme-modern-experience-timeline::before {
    left: 50%;
    transform: translateX(-50%);
  }
}

.theme-modern-experience-item {
  position: relative;
  margin-bottom: 3rem;
  padding-left: 5rem;
}

@media (min-width: 768px) {
  .theme-modern-experience-item {
    padding-left: 0;
    width: 50%;
  }

  .theme-modern-experience-item:nth-child(even) {
    margin-left: 50%;
    padding-left: 3rem;
  }

  .theme-modern-experience-item:nth-child(odd) {
    padding-right: 3rem;
    text-align: right;
  }
}

.theme-modern-experience-timeline-dot {
  position: absolute;
  left: 1.25rem;
  top: 1rem;
  width: 1.5rem;
  height: 1.5rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
  z-index: 2;
}

@media (min-width: 768px) {
  .theme-modern-experience-timeline-dot {
    left: 50%;
    transform: translateX(-50%);
  }
}

.theme-modern-experience-content {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.theme-modern-experience-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.theme-modern-experience-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.theme-modern-experience-title-group {
  flex: 1;
  min-width: 0;
}

.theme-modern-experience-role {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}

.theme-modern-experience-company-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.theme-modern-experience-company {
  font-weight: 500;
  color: #3b82f6;
  font-size: 1rem;
}

.theme-modern-experience-company-link {
  color: #3b82f6;
  transition: color 0.2s ease;
}

.theme-modern-experience-company-link:hover {
  color: #1d4ed8;
}

.theme-modern-experience-link-icon {
  width: 1rem;
  height: 1rem;
}

.theme-modern-experience-icon {
  width: 1rem;
  height: 1rem;
  color: #64748b;
}

.theme-modern-experience-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-end;
  text-align: right;
}

@media (max-width: 767px) {
  .theme-modern-experience-meta {
    align-items: flex-start;
    text-align: left;
  }
}

.theme-modern-experience-duration {
  font-weight: 500;
  color: #64748b;
  font-size: 0.875rem;
  background: #f1f5f9;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
}

.theme-modern-experience-location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.theme-modern-experience-location-text {
  font-size: 0.875rem;
  color: #64748b;
}

.theme-modern-experience-description {
  color: #475569;
  line-height: 1.6;
  margin: 1rem 0 0 0;
}

.theme-modern-experience-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.theme-modern-experience-delete-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-modern-experience-delete-btn:hover {
  background: #dc2626;
  transform: scale(1.05);
}

.theme-modern-experience-delete-icon {
  width: 1rem;
  height: 1rem;
}

.theme-modern-experience-add-container {
  text-align: center;
  margin-top: 2rem;
}

.theme-modern-experience-add-btn {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.theme-modern-experience-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.theme-modern-experience-add-icon {
  width: 1.25rem;
  height: 1.25rem;
}


/* ===== SKILLS STYLES ===== */
/* Modern Theme - Skills Component */

.theme-modern-skills {
  padding: 5rem 0;
  background: white;
  position: relative;
}

.theme-modern-skills-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .theme-modern-skills-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-skills-container {
    padding: 0 2rem;
  }
}

.theme-modern-skills-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #1e293b;
  position: relative;
}

.theme-modern-skills-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

.theme-modern-skills-content {
  max-width: 1000px;
  margin: 0 auto;
}

/* Skills Badges Grid */
.theme-modern-skills-badges-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
  align-items: flex-start;
}

/* Individual Skill Badge */
.theme-modern-skill-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(59, 130, 246, 0.1);
  border-radius: 1.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  transition: all 0.3s ease;
  box-shadow:
    0 2px 10px rgba(59, 130, 246, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
  cursor: default;
  overflow: hidden;
}

.theme-modern-skill-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(139, 92, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.theme-modern-skill-badge:hover::before {
  opacity: 1;
}

.theme-modern-skill-badge:hover {
  transform: translateY(-2px) scale(1.02);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.08);
}

.theme-modern-skill-badge-text {
  position: relative;
  z-index: 1;
}

/* Editing Mode Styles */
.theme-modern-skill-badge-edit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.theme-modern-skill-badge-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: 0.95rem;
  font-weight: 600;
  color: #1e293b;
  padding: 0;
  min-width: 80px;
}

.theme-modern-skill-badge-input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.theme-modern-skill-badge-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.theme-modern-skill-badge-delete:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.theme-modern-skill-badge-delete-icon {
  width: 0.75rem;
  height: 0.75rem;
}

/* Editing state styling */
.theme-modern-skill-badge:has(.theme-modern-skill-badge-edit) {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
  border-style: dashed;
}

.theme-modern-skill-badge:has(.theme-modern-skill-badge-edit):hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
}

/* Add Button */
.theme-modern-skills-add-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.theme-modern-skills-add-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.theme-modern-skills-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.theme-modern-skills-add-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .theme-modern-skills {
    padding: 3rem 0;
  }

  .theme-modern-skills-title {
    font-size: 2rem;
  }

  .theme-modern-skills-badges-grid {
    gap: 0.75rem;
  }

  .theme-modern-skill-badge {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}





/* Input styles for experience section */
.theme-modern-experience-role-input,
.theme-modern-experience-company-input,
.theme-modern-experience-duration-input,
.theme-modern-experience-location-input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  background: white;
  width: 100%;
}

.theme-modern-experience-role-input {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.theme-modern-experience-company-input {
  font-weight: 500;
  color: #3b82f6;
  font-size: 1rem;
}

.theme-modern-experience-duration-input {
  font-weight: 500;
  color: #64748b;
  font-size: 0.875rem;
}

.theme-modern-experience-location-input {
  font-size: 0.875rem;
  color: #64748b;
}

.theme-modern-experience-description-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  color: #475569;
  background: white;
  resize: vertical;
  min-height: 80px;
}

.theme-modern-experience-role-input:focus,
.theme-modern-experience-company-input:focus,
.theme-modern-experience-duration-input:focus,
.theme-modern-experience-location-input:focus,
.theme-modern-experience-description-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}


/* ===== PROJECTS STYLES ===== */
/* Modern Theme - Projects Component */

.theme-modern-projects {
  padding: 5rem 1rem;
  background: #111827;
  color: white;
  position: relative;
  overflow: hidden;
}

.theme-modern-projects::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255,255,255,0.02) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: pulse 15s ease-in-out infinite;
}

.theme-modern-projects::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, transparent 100%);
  filter: blur(3rem);
}

.theme-modern-projects-container {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.theme-modern-projects-header {
  text-align: center;
  margin-bottom: 4rem;
}

.theme-modern-projects-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #60a5fa, #a78bfa);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 640px) {
  .theme-modern-projects-title {
    font-size: 3rem;
  }
}

.theme-modern-projects-subtitle {
  font-size: 1.125rem;
  color: #9ca3af;
  max-width: 600px;
  margin: 0 auto;
}

.theme-modern-projects-grid {
  display: grid;
  gap: 2rem;
}

@media (min-width: 768px) {
  .theme-modern-projects-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .theme-modern-projects-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.theme-modern-project-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-modern-project-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  border-color: rgba(147, 51, 234, 0.3);
}

.theme-modern-project-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.theme-modern-project-content {
  padding: 1.5rem;
}

.theme-modern-project-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
}

.theme-modern-project-description {
  color: #d1d5db;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.theme-modern-project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #60a5fa;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.theme-modern-project-link:hover {
  color: #93c5fd;
}

.theme-modern-project-link-icon {
  width: 1rem;
  height: 1rem;
}

.theme-modern-project-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.theme-modern-project-remove-btn {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.theme-modern-project-remove-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  transform: scale(1.1);
}

.theme-modern-project-remove-icon {
  width: 1rem;
  height: 1rem;
}

.theme-modern-project-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.theme-modern-project-upload-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  z-index: 10;
}

.theme-modern-project-card:hover .theme-modern-project-upload-overlay {
  opacity: 1;
}

.theme-modern-project-upload-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
}

.theme-modern-project-upload-icon {
  width: 2rem;
  height: 2rem;
  color: white;
}

.theme-modern-project-upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.theme-modern-project-upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.theme-modern-project-upload-text {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
}

.theme-modern-project-image-uploading {
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.theme-modern-project-loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
}

.theme-modern-project-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: white;
}

.theme-modern-project-loading-spinner {
  width: 2rem;
  height: 2rem;
  animation: spin 1s linear infinite;
}

.theme-modern-project-loading-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.theme-modern-project-add-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-modern-project-add-card:hover {
  border-color: rgba(147, 51, 234, 0.5);
  background: rgba(147, 51, 234, 0.05);
  transform: translateY(-4px);
}

.theme-modern-project-add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #9ca3af;
}

.theme-modern-project-add-card:hover .theme-modern-project-add-content {
  color: #a78bfa;
}

.theme-modern-project-add-icon {
  width: 3rem;
  height: 3rem;
}

.theme-modern-project-add-text {
  font-size: 1.125rem;
  font-weight: 600;
}


/* ===== CONTACT STYLES ===== */
/* Modern Theme - Contact Component */

.theme-modern-contact {
  padding: 5rem 0;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  color: white;
}

.theme-modern-contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  text-align: center;
}

.theme-modern-contact-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(90deg, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 768px) {
  .theme-modern-contact-title {
    font-size: 3rem;
  }
}

.theme-modern-contact-subtitle {
  font-size: 1.125rem;
  color: #9ca3af;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.theme-modern-contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 3rem;
  align-items: center;
}

@media (min-width: 768px) {
  .theme-modern-contact-info {
    flex-direction: row;
    justify-content: center;
    gap: 3rem;
  }
}

.theme-modern-contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.theme-modern-contact-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.theme-modern-contact-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #8b5cf6;
}

.theme-modern-contact-text {
  color: white;
  font-weight: 500;
}

.theme-modern-social-links {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.theme-modern-social-item {
  display: flex;
  align-items: center;
}

.theme-modern-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #9ca3af;
  transition: all 0.3s ease;
}

.theme-modern-social-link:hover {
  background: rgba(139, 92, 246, 0.2);
  border-color: #8b5cf6;
  color: #8b5cf6;
  transform: translateY(-2px);
}

.theme-modern-social-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.theme-modern-social-edit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-modern-social-url {
  color: #9ca3af;
  font-size: 0.875rem;
}


/* ===== FOOTER STYLES ===== */
/* Modern Theme - Footer Component */

.theme-modern-footer {
  background: #1f2937;
  color: #d1d5db;
  padding: 3rem 1rem 2rem;
  text-align: center;
}

.theme-modern-footer-container {
  max-width: 1200px;
  margin: 0 auto;
}

.theme-modern-footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.theme-modern-footer-text {
  margin-bottom: 0;
}

.theme-modern-footer-attribution {
  color: #6b7280;
  font-size: 0.875rem;
}

.theme-modern-footer-link {
  color: #60a5fa;
  text-decoration: none;
  transition: color 0.3s ease;
}

.theme-modern-footer-link:hover {
  color: #93c5fd;
}


/* ===== BASE STYLES ===== */
/* Base styles and reset */
.theme-modern-root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #333;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

.theme-modern-root *,
.theme-modern-root *::before,
.theme-modern-root *::after {
  box-sizing: inherit;
}

/* Container utilities */
.theme-modern-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .theme-modern-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-container {
    padding: 0 2rem;
  }
}

/* Utility classes */
.theme-modern-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.theme-modern-btn-primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
}

.theme-modern-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

.theme-modern-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-modern-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Animations */
@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.1; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

