"use client";
import { ProfolifyThemeProps } from "@/lib/types";
import { CreativeNavbar } from "./CreativeNavbar";
import { CreativeHero } from "./CreativeHero";
import { CreativeAbout } from "./CreativeAbout";
import { CreativeExperience } from "./CreativeExperience";
import { CreativeSkills } from "./CreativeSkills";
import { CreativeProjects } from "./CreativeProjects";
import { CreativeContact } from "./CreativeContact";
import { CreativeFooter } from "./CreativeFooter";

export function CreativeMinimalistTheme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    // Helper function to check if section should be shown
    // If not editing and no data, don't show section
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
    const shouldShowSection = (sectionData: any, fallbackCheck?: () => boolean) => {
        if (isEditing) return true; // Always show in editing mode
        if (fallbackCheck) return fallbackCheck();
        return sectionData && Object.values(sectionData).some(value =>
            value !== null && value !== undefined && value !== ''
        );
    };

    return (
        <div className="theme-creative-root">
            <CreativeNavbar isEditing={isEditing} serverData={serverData} />
            <main>
                <CreativeHero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <CreativeAbout isEditing={isEditing} serverData={serverData} />
                <CreativeExperience isEditing={isEditing} serverData={serverData} />
                <CreativeSkills isEditing={isEditing} serverData={serverData} />
                <CreativeProjects isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <CreativeContact isEditing={isEditing} serverData={serverData} />
            </main>
            <CreativeFooter isEditing={isEditing} serverData={serverData} />
        </div>
    );
};
