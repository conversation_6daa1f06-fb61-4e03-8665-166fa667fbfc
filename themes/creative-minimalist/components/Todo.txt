#Todo

1. Make work and project section and skills section show the initial one item automatically withut clicking add button like showing the dispatch one 

2. Improve the modernt theme each section

3. Testing



here as i noticied in the @/home/<USER>/Desktop/profolify/themes/creative-minimalist/components/CreativeExperience.tsx @/home/<USER>/Desktop/profolify/themes/modern/components/ModernExperience.tsx @/home/<USER>/Desktop/profolify/themes/creative-minimalist/components/CreativeSkills.tsx @/home/<USER>/Desktop/profolify/themes/modern/components/ModernSkills.tsx @/home/<USER>/Desktop/profolify/themes/creative-minimalist/components/CreativeProjects.tsx @/home/<USER>/Desktop/profolify/themes/modern/components/ModernProjects.tsx  when as a first user if i chose tempate then i see on these section to empty add button but i want to have one of the item already loaded 
for examp in the creative experence section 
 we display this sectio only when we dispatch the add button 

     const newExperience: Experience = {
                id: Date.now().toString(),
                role: 'New Role',
                company: 'Company Name',
                duration: 'May 2020 - Present',
                description: 'Describe your key responsibilities and achievements...',
                location: 'Location'
            };

but i already want this to be displact one card autocticlly when template is chosed and loaded 


default item automatically when a template is first loaded, instead of showing empty sections with just an "Add" button. Let me examine the current implementation and then modify it to include default dummy data.