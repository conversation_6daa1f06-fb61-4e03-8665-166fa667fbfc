"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { Mail, Phone, Github, Linkedin, Twitter } from "lucide-react";
import { useAuthStore } from "@/stores/auth-store";

export function ModernContact({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const { user } = useAuthStore();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    // Use user's email as default if no email is set
    const defaultEmail = user?.email || '<EMAIL>';

    const handleUpdate = (field: keyof Omit<PortfolioData, 'projects'>, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
        }
    };

    const contactItems = [
        {
            icon: Mail,
            label: "Email",
            value: data.email || defaultEmail,
            field: 'email' as keyof Omit<PortfolioData, 'projects'>,
            href: `mailto:${data.email || defaultEmail}`
        },
        {
            icon: Phone,
            label: "Phone",
            value: data.phone || 'Your phone number',
            field: 'phone' as keyof Omit<PortfolioData, 'projects'>,
            href: `tel:${data.phone}`
        }
    ];

    const socialLinks = [
        {
            icon: Github,
            url: data.githubUrl,
            field: 'githubUrl' as keyof Omit<PortfolioData, 'projects'>,
            label: "GitHub"
        },
        {
            icon: Linkedin,
            url: data.linkedinUrl,
            field: 'linkedinUrl' as keyof Omit<PortfolioData, 'projects'>,
            label: "LinkedIn"
        },
        {
            icon: Twitter,
            url: data.twitterUrl,
            field: 'twitterUrl' as keyof Omit<PortfolioData, 'projects'>,
            label: "Twitter"
        }
    ];

    return (
        <section id="contact" className="theme-modern-contact">
            <div className="theme-modern-contact-container">
                <h2 className="theme-modern-contact-title">Get In Touch</h2>
                <p className="theme-modern-contact-subtitle">
                    Ready to collaborate? Let&#39;s discuss your next project and bring your ideas to life.
                </p>

                <div className="theme-modern-contact-info">
                    {contactItems.map((item) => {
                        const IconComponent = item.icon;
                        // Always show email, only show phone if it has a value or is being edited
                        if (item.field === 'phone' && !item.value && !isEditing) return null;
                        if (item.field === 'phone' && item.value === 'Your phone number' && !isEditing) return null;

                        return (
                            <div key={item.field} className="theme-modern-contact-item">
                                <IconComponent className="theme-modern-contact-icon" />
                                <EditableText
                                    isEditing={isEditing}
                                    tagName="span"
                                    className="theme-modern-contact-text"
                                    initialValue={item.value || `Your ${item.label}`}
                                    onSave={(value) => handleUpdate(item.field, value)}
                                />
                            </div>
                        );
                    })}
                </div>

                {(isEditing || socialLinks.some(link => link.url)) && (
                    <div className="theme-modern-social-links">
                        {socialLinks.map((social) => {
                        const IconComponent = social.icon;
                        if (!social.url && !isEditing) return null;
                        
                        return (
                            <div key={social.field} className="theme-modern-social-item">
                                {isEditing ? (
                                    <div className="theme-modern-social-edit">
                                        <IconComponent className="theme-modern-social-icon" />
                                        <EditableText
                                            isEditing={isEditing}
                                            tagName="span"
                                            className="theme-modern-social-url"
                                            initialValue={social.url || `Your ${social.label} URL`}
                                            onSave={(value) => handleUpdate(social.field, value)}
                                        />
                                    </div>
                                ) : (
                                    <a
                                        href={social.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="theme-modern-social-link"
                                        aria-label={social.label}
                                    >
                                        <IconComponent className="theme-modern-social-icon" />
                                    </a>
                                )}
                            </div>
                        );
                        })}
                    </div>
                )}
            </div>
        </section>
    );
};
