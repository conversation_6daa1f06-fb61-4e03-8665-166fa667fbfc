"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { Experience } from "@/lib/types";
import { Plus, Trash2, Briefcase, Clock, MapPin } from "lucide-react";

interface ExperienceItemProps {
    experience: Experience;
    isEditing: boolean;
    onUpdate: (id: string, field: keyof Experience, value: string) => void;
    onDelete: (id: string) => void;
}

const ExperienceItem = ({ experience, isEditing, onUpdate, onDelete }: ExperienceItemProps) => {
    return (
        <div className="modern-experience-card">
            {/* Card Header with Role and Company */}
            <div className="modern-experience-header">
                <div className="modern-experience-role-section">
                    <div className="modern-experience-briefcase-icon">
                        <Briefcase size={20} />
                    </div>
                    <div className="modern-experience-role-content">
                        {isEditing ? (
                            <input
                                type="text"
                                value={experience.role}
                                onChange={(e) => onUpdate(experience.id, 'role', e.target.value)}
                                className="modern-experience-role-input editable-field editable-field-modern"
                                placeholder="Job Title"
                                data-placeholder="Job Title"
                            />
                        ) : (
                            <h3 className="modern-experience-role">{experience.role}</h3>
                        )}

                        {isEditing ? (
                            <input
                                type="text"
                                value={experience.company}
                                onChange={(e) => onUpdate(experience.id, 'company', e.target.value)}
                                className="modern-experience-company-input editable-field editable-field-modern"
                                placeholder="Company Name"
                                data-placeholder="Company Name"
                            />
                        ) : (
                            <p className="modern-experience-company">{experience.company}</p>
                        )}
                    </div>
                </div>

                {isEditing && (
                    <button
                        onClick={() => onDelete(experience.id)}
                        className="modern-experience-delete-btn"
                        title="Delete Experience"
                    >
                        <Trash2 size={16} />
                    </button>
                )}
            </div>

            {/* Card Meta Information */}
            <div className="modern-experience-meta">
                <div className="modern-experience-duration">
                    <Clock size={16} className="modern-experience-meta-icon" />
                    {isEditing ? (
                        <input
                            type="text"
                            value={experience.duration}
                            onChange={(e) => onUpdate(experience.id, 'duration', e.target.value)}
                            className="modern-experience-duration-input editable-field-inline editable-field-modern"
                            placeholder="Duration"
                            data-placeholder="e.g., Jan 2020 - Present"
                        />
                    ) : (
                        <span className="modern-experience-duration-text">{experience.duration}</span>
                    )}
                </div>

                {(experience.location || isEditing) && (
                    <div className="modern-experience-location">
                        <MapPin size={16} className="modern-experience-meta-icon" />
                        {isEditing ? (
                            <input
                                type="text"
                                value={experience.location || ''}
                                onChange={(e) => onUpdate(experience.id, 'location', e.target.value)}
                                className="modern-experience-location-input editable-field-inline editable-field-modern"
                                placeholder="Location"
                                data-placeholder="Location (optional)"
                            />
                        ) : (
                            <span className="modern-experience-location-text">{experience.location}</span>
                        )}
                    </div>
                )}
            </div>

            {/* Card Description */}
            {(experience.description || isEditing) && (
                <div className="modern-experience-description">
                    {isEditing ? (
                        <textarea
                            value={experience.description || ''}
                            onChange={(e) => onUpdate(experience.id, 'description', e.target.value)}
                            className="modern-experience-description-input editable-field-large editable-field-modern"
                            placeholder="Describe your key responsibilities and achievements..."
                            data-placeholder="Describe your key responsibilities and achievements in this role..."
                            rows={4}
                        />
                    ) : (
                        <p className="modern-experience-description-text">{experience.description}</p>
                    )}
                </div>
            )}
        </div>
    );
};

export function ModernExperience({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleAddExperience = () => {
        if (dispatch) {
            const newExperience: Experience = {
                id: Date.now().toString(),
                role: 'Software Engineer',
                company: 'Tech Company',
                duration: '2023 - Present',
                description: 'Developing innovative solutions and contributing to exciting projects. Click to edit and add your experience details.',
                location: 'Remote'
            };

            const currentExperiences = data.experiences || [];
            const updatedExperiences = [...currentExperiences, newExperience];

            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'experiences',
                    value: updatedExperiences
                }
            });
        }
    };

    const handleUpdateExperience = (id: string, field: keyof Experience, value: string) => {
        if (dispatch) {
            const currentExperiences = data.experiences || [];
            const updatedExperiences = currentExperiences.map(exp =>
                exp.id === id ? { ...exp, [field]: value } : exp
            );
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'experiences',
                    value: updatedExperiences
                }
            });
        }
    };

    const handleDeleteExperience = (id: string) => {
        if (dispatch) {
            const currentExperiences = data.experiences || [];
            const updatedExperiences = currentExperiences.filter(exp => exp.id !== id);
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'experiences',
                    value: updatedExperiences
                }
            });
        }
    };

    const experiences = data.experiences || [];

    return (
        <section id="experience" className="modern-experience">
            <div className="modern-experience-container">
                {/* Section Header */}
                <div className="modern-experience-header">
                    <h2 className="modern-experience-title">Work Experience</h2>
                    <div className="modern-experience-divider"></div>
                    <p className="modern-experience-subtitle">
                        My professional journey and key achievements
                    </p>
                </div>

                {/* Experience Cards Grid */}
                <div className="modern-experience-grid">
                    {experiences.map((experience) => (
                        <ExperienceItem
                            key={experience.id}
                            experience={experience}
                            isEditing={isEditing}
                            onUpdate={handleUpdateExperience}
                            onDelete={handleDeleteExperience}
                        />
                    ))}
                </div>

                {/* Add Experience Button */}
                {isEditing && (
                    <div className="modern-experience-add-section">
                        <button
                            onClick={handleAddExperience}
                            className="modern-experience-add-btn"
                        >
                            <Plus size={20} />
                            Add Experience
                        </button>
                    </div>
                )}
            </div>
        </section>
    );
};
