"use client";
import { EditableText } from "@/components/ui/EditableText";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { Upload, Trash2, ExternalLink, Loader2, Plus, FolderOpen } from "lucide-react";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";
import { Project } from "@/lib/types";

interface ProjectCardProps {
    project: Project;
    index: number;
    isEditing: boolean;
    isUploading: boolean;
    onUpdate: (index: number, field: keyof Project, value: string) => void;
    onImageUpload: (event: React.ChangeEvent<HTMLInputElement>, projectId: string) => void;
    onRemove: (projectId: string) => void;
    isExport: boolean;
}

const ProjectCard = ({ project, index, isEditing, isUploading, onUpdate, onImageUpload, onRemove }: ProjectCardProps) => {
    return (
        <div className="modern-project-card">
            {/* Project Image */}
            <div className="modern-project-image-container">
                {isEditing && (
                    <div className="modern-project-upload-overlay">
                        <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => onImageUpload(e, project.id)}
                            className="modern-project-upload-input"
                            disabled={isUploading}
                        />
                        {isUploading ? (
                            <div className="modern-project-upload-loading">
                                <Loader2 className="modern-project-upload-icon modern-spinner" />
                                <span className="modern-project-upload-text">Uploading...</span>
                            </div>
                        ) : (
                            <div className="modern-project-upload-content">
                                <Upload className="modern-project-upload-icon" />
                                <span className="modern-project-upload-text">Upload Image</span>
                            </div>
                        )}
                    </div>
                )}
                <PortfolioImage
                    isEditing={isEditing}
                    src={project.imageUrl || 'https://placehold.co/400x250/1e293b/60a5fa?text=Project+Image'}
                    alt={project.title}
                    width={400}
                    height={250}
                    className={`modern-project-image ${isUploading ? 'modern-project-image-uploading' : ''}`}
                />

                {isUploading && (
                    <div className="modern-project-loading-overlay">
                        <div className="modern-project-loading-content">
                            <Loader2 className="modern-project-loading-spinner" />
                            <p className="modern-project-loading-text">Uploading image...</p>
                        </div>
                    </div>
                )}
            </div>

            {/* Project Content */}
            <div className="modern-project-content">
                {/* Project Header */}
                <div className="modern-project-header">
                    <div className="modern-project-title-section">
                        <div className="modern-project-folder-icon">
                            <FolderOpen size={20} />
                        </div>
                        <EditableText
                            isEditing={isEditing}
                            tagName="h3"
                            className={`modern-project-title ${isEditing ? 'editable-field editable-field-modern' : ''}`}
                            initialValue={project.title}
                            placeholder="Project Title"
                            onSave={(value) => onUpdate(index, 'title', value)}
                        />
                    </div>

                    {isEditing && (
                        <button
                            onClick={() => onRemove(project.id)}
                            className="modern-project-delete-btn"
                            title="Delete Project"
                        >
                            <Trash2 size={16} />
                        </button>
                    )}
                </div>

                {/* Project Description */}
                <div className="modern-project-description">
                    <EditableText
                        isEditing={isEditing}
                        tagName="div"
                        className={`modern-project-description-text ${isEditing ? 'editable-field-large editable-field-modern' : ''}`}
                        initialValue={project.description}
                        placeholder="Describe your project, technologies used, and key features..."
                        onSave={(value) => onUpdate(index, 'description', value)}
                    />
                </div>

                {/* Project URL */}
                <div className="modern-project-url">
                    {isEditing ? (
                        <>
                            <EditableText
                                isEditing={isEditing}
                                tagName="div"
                                className="modern-project-url-input editable-field-inline editable-field-modern"
                                initialValue={project.liveUrl || ''}
                                placeholder="https://project-url.com (optional)"
                                onSave={(value) => onUpdate(index, 'liveUrl', value)}
                            />

                        </>
                    ) : (
                        project.liveUrl && (
                            <a
                                href={project.liveUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="modern-project-link"
                            >
                                <ExternalLink size={16} />
                                View Project
                            </a>
                        )
                    )}
                </div>
            </div>
        </div>
    );
};

export function ModernProjects({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = useEditorSafe();
    const isExport = useIsExport();

    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleUpdate = (index: number, field: keyof Project, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_PROJECT', payload: { index, field, value } });
        }
    };

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>, projectId: string) => {
        const file = event.target.files?.[0];
        if (file && onImageUpload) {
            onImageUpload({ file, type: 'project', id: projectId });
        }
    };

    const removeProject = (projectId: string) => {
        if (dispatch) {
            dispatch({ type: 'DELETE_PROJECT', payload: { id: projectId } });
        }
    };

    const addProject = () => {
        if (dispatch) {
            dispatch({ type: 'ADD_PROJECT' });
        }
    };

    const projects = data.projects || [];

    return (
        <section id="projects" className="modern-projects">
            <div className="modern-projects-container">
                {/* Section Header */}
                <div className="modern-projects-header">
                    <h2 className="modern-projects-title">Featured Projects</h2>
                    <div className="modern-projects-divider"></div>
                    <p className="modern-projects-subtitle">
                        A showcase of projects that demonstrate technical expertise and creative problem-solving
                    </p>
                </div>

                {/* Projects Grid */}
                <div className="modern-projects-grid">
                    {projects.map((project, index) => (
                        <ProjectCard
                            key={project.id}
                            project={project}
                            index={index}
                            isEditing={isEditing}
                            isUploading={isEditing && context?.state.isUploading?.type === 'project' && context.state.isUploading.id === project.id}
                            onUpdate={handleUpdate}
                            onImageUpload={handleImageUpload}
                            onRemove={removeProject}
                            isExport={isExport}
                        />
                    ))}

                    {/* Add Project Button */}
                    {isEditing && (
                        <div className="modern-projects-add-section">
                            <button onClick={addProject} className="modern-projects-add-btn">
                                <Plus size={20} />
                                Add Project
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};
