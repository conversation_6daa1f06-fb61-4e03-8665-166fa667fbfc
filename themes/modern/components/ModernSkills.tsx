"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";

import { Skill } from "@/lib/types";
import { Plus, Trash2 } from "lucide-react";

interface SkillItemProps {
    skill: Skill;
    isEditing: boolean;
    onUpdate: (id: string, field: keyof Skill, value: string) => void;
    onDelete: (id: string) => void;
}



const SkillItem = ({ skill, isEditing, onUpdate, onDelete }: SkillItemProps) => {
    return (
        <div className="theme-modern-skill-badge">
            {isEditing ? (
                <div className="theme-modern-skill-badge-edit">
                    <input
                        type="text"
                        value={skill.name}
                        onChange={(e) => onUpdate(skill.id, 'name', e.target.value)}
                        className="theme-modern-skill-badge-input"
                        placeholder="Enter skill name"
                    />
                    <button
                        onClick={() => onDelete(skill.id)}
                        className="theme-modern-skill-badge-delete"
                        title="Delete skill"
                    >
                        <Trash2 className="theme-modern-skill-badge-delete-icon" />
                    </button>
                </div>
            ) : (
                <span className="theme-modern-skill-badge-text">{skill.name}</span>
            )}
        </div>
    );
};

export function ModernSkills({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleAddSkill = () => {
        if (dispatch) {
            const newSkill: Skill = {
                id: Date.now().toString(),
                name: 'New Skill'
            };

            // If we only have default skills, replace them with the new one
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.length === 0
                ? [newSkill]
                : [...currentSkills, newSkill];

            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleUpdateSkill = (id: string, field: keyof Skill, value: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.map(skill =>
                skill.id === id ? { ...skill, [field]: value } : skill
            );
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleDeleteSkill = (id: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.filter(skill => skill.id !== id);
            // When deleting, always update the actual data (even if it becomes empty)
            // The display logic will handle showing dummy when needed
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };


    const skills = data.skills || [];

    // Don't render if no skills and not editing
    if (!isEditing && skills.length === 0) {
        return null;
    }

    return (
        <section id="skills" className="theme-modern-skills">
            <div className="theme-modern-skills-container">
                <h2 className="theme-modern-skills-title">Skills & Technologies</h2>
                
                <div className="theme-modern-skills-content">
                    <div className="theme-modern-skills-badges-grid">
                        {skills.map((skill) => (
                            <SkillItem
                                key={skill.id}
                                skill={skill}
                                isEditing={isEditing}
                                onUpdate={handleUpdateSkill}
                                onDelete={handleDeleteSkill}
                            />
                        ))}
                    </div>

                    {isEditing && (
                        <div className="theme-modern-skills-add-container">
                            <button
                                onClick={handleAddSkill}
                                className="theme-modern-skills-add-btn"
                            >
                                <Plus className="theme-modern-skills-add-icon" />
                                Add Skill
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};
