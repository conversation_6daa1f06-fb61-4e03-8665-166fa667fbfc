/* Modern Theme - About Component */

.theme-modern-about {
  padding: 5rem 1rem;
  background: #f9fafb;
}

.theme-modern-about-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  gap: 3rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .theme-modern-about-container {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }
}

.theme-modern-about-content {
  text-align: center;
}

@media (min-width: 1024px) {
  .theme-modern-about-content {
    text-align: left;
  }
}

.theme-modern-about-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1f2937;
  margin-bottom: 1.5rem;
}

@media (min-width: 640px) {
  .theme-modern-about-title {
    font-size: 3rem;
  }
}

.theme-modern-about-text {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.theme-modern-about-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}
