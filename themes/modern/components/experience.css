/* Modern Theme - Experience Section - Completely Refactored */

/* Shared Editable Field Styles */
/* Base editable field styling - only applies when contenteditable */
.editable-field[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1.5rem;
  cursor: text;
}

.editable-field[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Placeholder styling for empty editable fields */
.editable-field[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Inline editable fields (for smaller text elements) - only applies when contenteditable */
.editable-field-inline[contenteditable="true"] {
  position: relative;
  border: 1px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 1rem;
  cursor: text;
  display: inline-block;
}

.editable-field-inline[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.editable-field-inline[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Large editable fields (for descriptions, content areas) - only applies when contenteditable */
.editable-field-large[contenteditable="true"] {
  position: relative;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
  min-height: 3rem;
  cursor: text;
}

.editable-field-large[contenteditable="true"]:hover {
  border-color: rgba(102, 126, 234, 0.5);
  background: rgba(102, 126, 234, 0.1);
}

.editable-field-large[contenteditable="true"]:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.editable-field-large[contenteditable="true"]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  font-style: italic;
  pointer-events: none;
}

/* Modern theme variant (blue gradient) - only applies when contenteditable */
.editable-field-modern[contenteditable="true"] {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.editable-field-modern[contenteditable="true"]:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.editable-field-modern[contenteditable="true"]:focus {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Main Section */
.modern-experience {
  padding: 5rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  position: relative;
  overflow: hidden;
}

.modern-experience::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 139, 250, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.modern-experience-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

/* Section Header */
.modern-experience-header {
  text-align: center;
  margin-bottom: 4rem;
}

.modern-experience-title {
  font-size: 3rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 640px) {
  .modern-experience-title {
    font-size: 3.5rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .modern-experience-title {
    font-size: 4rem;
    letter-spacing: -0.075em;
  }
}

.modern-experience-divider {
  width: 4rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 0.125rem;
  margin: 0 auto 1.5rem;
}

.modern-experience-subtitle {
  font-size: 1.25rem;
  color: #cbd5e1;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
}

@media (min-width: 768px) {
  .modern-experience-subtitle {
    font-size: 1.375rem;
  }
}

/* Experience Grid */
.modern-experience-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .modern-experience-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
  }
}

/* Experience Cards */
.modern-experience-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(96, 165, 250, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-experience-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(168, 139, 250, 0.05) 100%);
  pointer-events: none;
}

.modern-experience-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #60a5fa 0%, #a78bfa 100%);
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.modern-experience-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  border-color: rgba(96, 165, 250, 0.4);
}

.modern-experience-card:hover::after {
  opacity: 1;
}

/* Card Header */
.modern-experience-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.modern-experience-role-section {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.modern-experience-briefcase-icon {
  color: #60a5fa;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.modern-experience-role-content {
  flex: 1;
  min-width: 0;
}

/* Role Styling */
.modern-experience-role {
  font-size: 1.375rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
}



/* Company Styling */
.modern-experience-company {
  font-size: 1rem;
  color: #60a5fa;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}



/* Delete Button */
.modern-experience-delete-btn {
  background: rgba(242, 74, 74, 0.953);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #f3eeee;
  padding: 0.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin: 0 1rem;
}

.modern-experience-delete-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(242, 74, 74, 0.953);
  transform: scale(1.05);
}

/* Meta Information */
.modern-experience-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.modern-experience-duration,
.modern-experience-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-experience-meta-icon {
  color: #94a3b8;
  flex-shrink: 0;
}

.modern-experience-duration-text,
.modern-experience-location-text {
  font-size: 0.875rem;
  color: #e2e8f0;
  font-weight: 500;
  margin: 0;
}



/* Description */
.modern-experience-description {
  position: relative;
  z-index: 1;
}

.modern-experience-description-text {
  font-size: 1rem;
  color: #cbd5e1;
  line-height: 1.7;
  margin: 0;
  white-space: pre-line;
}



/* Add Experience Section */
.modern-experience-add-section {
  text-align: center;
  margin-top: 3rem;
}

.modern-experience-add-btn {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  border: none;
  color: #ffffff;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 20px rgba(96, 165, 250, 0.3);
}

.modern-experience-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(96, 165, 250, 0.4);
}

/* Responsive Design */
@media (max-width: 640px) {
  .modern-experience {
    padding: 3rem 0;
  }

  .modern-experience-title {
    font-size: 2.5rem;
  }

  .modern-experience-card {
    padding: 1.5rem;
  }

  .modern-experience-role {
    font-size: 1.25rem;
  }

  .modern-experience-header {
    flex-direction: column;
    gap: 1rem;
  }

  .modern-experience-delete-btn {
    align-self: flex-end;
  }
}
