/* Modern Theme - Experience Component */

.theme-modern-experience {
  padding: 6rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  position: relative;
  overflow: hidden;
}

.theme-modern-experience::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(168, 139, 250, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.theme-modern-experience-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

@media (min-width: 640px) {
  .theme-modern-experience-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-experience-container {
    padding: 0 2rem;
  }
}

.theme-modern-experience-header {
  text-align: center;
  margin-bottom: 4rem;
}

.theme-modern-experience-title {
  font-size: 2.5rem;
  font-weight: 900;
  color: #ffffff;
  margin-bottom: 1rem;
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (min-width: 640px) {
  .theme-modern-experience-title {
    font-size: 3rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .theme-modern-experience-title {
    font-size: 3.5rem;
    letter-spacing: -0.075em;
  }
}

.theme-modern-experience-divider {
  width: 4rem;
  height: 0.25rem;
  background: linear-gradient(90deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 0.125rem;
  margin: 0 auto 1.5rem auto;
}

.theme-modern-experience-subtitle {
  font-size: 1.125rem;
  color: #cbd5e1;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

@media (min-width: 640px) {
  .theme-modern-experience-subtitle {
    font-size: 1.25rem;
  }
}

.theme-modern-experience-timeline {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
}

.theme-modern-experience-timeline::before {
  content: '';
  position: absolute;
  left: 2rem;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%);
  border-radius: 1.5px;
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.3);
}

@media (min-width: 768px) {
  .theme-modern-experience-timeline::before {
    left: 50%;
    transform: translateX(-50%);
  }
}

.theme-modern-experience-item {
  position: relative;
  margin-bottom: 4rem;
  padding-left: 5rem;
}

@media (min-width: 768px) {
  .theme-modern-experience-item {
    padding-left: 0;
    width: 50%;
  }

  .theme-modern-experience-item:nth-child(even) {
    margin-left: 50%;
    padding-left: 3rem;
  }

  .theme-modern-experience-item:nth-child(odd) {
    padding-right: 3rem;
  }

  /* Remove text-align: right for better readability */
  .theme-modern-experience-item:nth-child(odd) .theme-modern-experience-card-header {
    flex-direction: row-reverse;
  }

  .theme-modern-experience-item:nth-child(odd) .theme-modern-experience-meta {
    align-items: flex-start;
    text-align: left;
  }
}

.theme-modern-experience-timeline-dot {
  position: absolute;
  left: 1.25rem;
  top: 1.5rem;
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  border-radius: 50%;
  border: 4px solid #1e293b;
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.4), 0 4px 15px rgba(0, 0, 0, 0.3);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.theme-modern-experience-timeline-dot:hover {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(96, 165, 250, 0.6), 0 6px 20px rgba(0, 0, 0, 0.4);
}

.theme-modern-experience-dot-inner {
  width: 0.5rem;
  height: 0.5rem;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

@media (min-width: 768px) {
  .theme-modern-experience-timeline-dot {
    left: 50%;
    transform: translateX(-50%);
  }

  .theme-modern-experience-timeline-dot:hover {
    transform: translateX(-50%) scale(1.1);
  }
}

.theme-modern-experience-content {
  position: relative;
}

.theme-modern-experience-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(96, 165, 250, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-modern-experience-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(168, 139, 250, 0.05) 100%);
  pointer-events: none;
}

.theme-modern-experience-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  border-color: rgba(96, 165, 250, 0.4);
}

.theme-modern-experience-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

@media (max-width: 767px) {
  .theme-modern-experience-card-header {
    flex-direction: column;
    gap: 1rem;
  }
}

.theme-modern-experience-title-group {
  flex: 1;
  min-width: 0;
}

.theme-modern-experience-role {
  font-size: 1.375rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.75rem 0;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

/* Input styling is now handled by shared editable field classes */
.theme-modern-experience-role-input {
  font-size: 1.375rem;
  font-weight: 700;
  color: #ffffff;
  width: 100%;
  background: transparent;
  border: none;
}

.theme-modern-experience-company-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.theme-modern-experience-company {
  font-weight: 600;
  color: #60a5fa;
  font-size: 1rem;
}

.theme-modern-experience-company-input {
  color: #60a5fa;
  font-weight: 600;
  background: transparent;
  border: none;
}

.theme-modern-experience-company-link {
  color: #60a5fa;
  transition: all 0.2s ease;
  padding: 0.25rem;
  border-radius: 0.25rem;
}

.theme-modern-experience-company-link:hover {
  color: #3b82f6;
  background: rgba(96, 165, 250, 0.1);
}

.theme-modern-experience-link-icon {
  width: 1rem;
  height: 1rem;
}

.theme-modern-experience-icon {
  width: 1rem;
  height: 1rem;
  color: #94a3b8;
}

.theme-modern-experience-meta {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: flex-end;
  text-align: right;
  min-width: 200px;
}

@media (max-width: 767px) {
  .theme-modern-experience-meta {
    align-items: flex-start;
    text-align: left;
    min-width: auto;
    width: 100%;
  }
}

.theme-modern-experience-duration {
  font-weight: 600;
  color: #e2e8f0;
  font-size: 0.875rem;
  background: linear-gradient(135deg, rgba(96, 165, 250, 0.2) 0%, rgba(168, 139, 250, 0.2) 100%);
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  border: 1px solid rgba(96, 165, 250, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.theme-modern-experience-duration-input {
  color: #e2e8f0;
  font-size: 0.875rem;
  font-weight: 600;
  background: transparent;
  border: none;
}

.theme-modern-experience-location {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.theme-modern-experience-location-text {
  font-size: 0.875rem;
  color: #94a3b8;
  font-weight: 500;
}

.theme-modern-experience-location-input {
  color: #94a3b8;
  font-size: 0.875rem;
  background: transparent;
  border: none;
}

.theme-modern-experience-description-container {
  margin-top: 1.5rem;
  position: relative;
  z-index: 1;
}

.theme-modern-experience-description {
  color: #cbd5e1;
  line-height: 1.7;
  margin: 0;
  font-size: 1rem;
  opacity: 0.9;
}

.theme-modern-experience-description-input {
  color: #cbd5e1;
  width: 100%;
  font-size: 1rem;
  line-height: 1.7;
  resize: vertical;
  min-height: 100px;
  background: transparent;
  border: none;
}

.theme-modern-experience-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
  position: relative;
  z-index: 1;
}

.theme-modern-experience-delete-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  padding: 0.625rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.theme-modern-experience-delete-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.theme-modern-experience-delete-icon {
  width: 1.125rem;
  height: 1.125rem;
}

.theme-modern-experience-add-container {
  text-align: center;
  margin-top: 3rem;
}

.theme-modern-experience-add-btn {
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 8px 25px rgba(96, 165, 250, 0.3);
}

.theme-modern-experience-add-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(96, 165, 250, 0.4);
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

.theme-modern-experience-add-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .theme-modern-experience {
    padding: 4rem 0;
  }

  .theme-modern-experience-header {
    margin-bottom: 3rem;
  }

  .theme-modern-experience-title {
    font-size: 2rem;
  }

  .theme-modern-experience-subtitle {
    font-size: 1rem;
  }

  .theme-modern-experience-item {
    margin-bottom: 3rem;
    padding-left: 4rem;
  }

  .theme-modern-experience-timeline-dot {
    width: 1.5rem;
    height: 1.5rem;
    left: 1rem;
  }

  .theme-modern-experience-timeline::before {
    left: 1.75rem;
    width: 2px;
  }

  .theme-modern-experience-card {
    padding: 1.5rem;
  }

  .theme-modern-experience-role {
    font-size: 1.25rem;
  }
}
