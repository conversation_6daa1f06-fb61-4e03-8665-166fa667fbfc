/* Modern Theme - Hero Component */

.theme-modern-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.theme-modern-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.theme-modern-hero-container {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.theme-modern-hero-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  line-height: 1.1;
}

@media (min-width: 640px) {
  .theme-modern-hero-title {
    font-size: 4rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-title {
    font-size: 5rem;
  }
}

.theme-modern-hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .theme-modern-hero-subtitle {
    font-size: 1.5rem;
  }
}

.theme-modern-hero-cta {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.theme-modern-hero-cta:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.theme-modern-hero-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
  min-height: 100vh;
  justify-content: center;
  padding: 5rem 0;
}

@media (min-width: 1024px) {
  .theme-modern-hero-layout {
    flex-direction: row;
    gap: 5rem;
  }
}

.theme-modern-hero-content {
  text-align: center;
  max-width: 600px;
}

@media (min-width: 1024px) {
  .theme-modern-hero-content {
    text-align: left;
  }
}

.theme-modern-hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

@media (min-width: 640px) {
  .theme-modern-hero-actions {
    flex-direction: row;
    justify-content: center;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-actions {
    justify-content: flex-start;
  }
}

.theme-modern-hero-action-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.theme-modern-hero-upload-btn {
  cursor: pointer;
}

.theme-modern-hero-upload-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

/* Hero Image Styles */
.theme-modern-hero-image-container {
  position: relative;
  flex-shrink: 0;
}

.theme-modern-hero-image-wrapper {
  position: relative;
  width: 16rem;
  height: 16rem;
}

@media (min-width: 1024px) {
  .theme-modern-hero-image-wrapper {
    width: 20rem;
    height: 20rem;
  }
}

.theme-modern-hero-image-wrapper::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, #8b5cf6, #ec4899, #06b6d4);
  border-radius: 50%;
  filter: blur(1rem);
  opacity: 0.6;
  animation: spin 10s linear infinite;
}

.theme-modern-hero-image-wrapper:hover::before {
  opacity: 0.8;
}

.theme-modern-hero-image-wrapper::after {
  content: '';
  position: absolute;
  inset: 0.5rem;
  background: #111827;
  border-radius: 50%;
  z-index: 1;
}

.theme-modern-hero-image {
  position: absolute;
  inset: 1rem;
  width: calc(100% - 2rem);
  height: calc(100% - 2rem);
  border-radius: 50%;
  object-fit: cover;
  z-index: 10;
}

.theme-modern-hero-upload-overlay {
  position: absolute;
  inset: 1rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
  z-index: 20;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.theme-modern-hero-image-wrapper:hover .theme-modern-hero-upload-overlay {
  opacity: 1;
}

.theme-modern-hero-upload-icon {
  width: 2rem;
  height: 2rem;
  margin-bottom: 0.5rem;
}

.theme-modern-hero-upload-loading {
  position: absolute;
  inset: 1rem;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 30;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.theme-modern-hero-upload-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: white;
}

.theme-modern-hero-upload-spinner {
  width: 4rem;
  height: 4rem;
  color: #8b5cf6;
  animation: spin 1s linear infinite;
}

.theme-modern-hero-upload-text {
  font-size: 1.125rem;
  font-weight: 600;
  background: linear-gradient(90deg, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
