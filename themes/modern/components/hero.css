/* Modern Theme - Hero Component (Dark Mode) */

.theme-modern-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #1e293b 75%, #0f172a 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.theme-modern-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(244, 114, 182, 0.05) 0%, transparent 50%);
  opacity: 0.8;
}

.theme-modern-hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="2" cy="2" r="1" fill="rgba(96,165,250,0.1)"/><circle cx="12" cy="12" r="0.5" fill="rgba(167,139,250,0.1)"/><circle cx="18" cy="6" r="0.5" fill="rgba(244,114,182,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.4;
}

.theme-modern-hero-container {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.theme-modern-hero-title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 25%, #60a5fa 50%, #a78bfa 75%, #f472b6 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.025em;
  text-shadow: 0 0 30px rgba(96, 165, 250, 0.3);
}

@media (min-width: 640px) {
  .theme-modern-hero-title {
    font-size: 4.5rem;
    letter-spacing: -0.05em;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-title {
    font-size: 6rem;
    letter-spacing: -0.075em;
  }
}

.theme-modern-hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2.5rem;
  color: #cbd5e1;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  font-weight: 400;
  line-height: 1.6;
}

@media (min-width: 640px) {
  .theme-modern-hero-subtitle {
    font-size: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-subtitle {
    font-size: 1.75rem;
  }
}

/* Button Styles */
.theme-modern-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.theme-modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.theme-modern-btn:hover::before {
  left: 100%;
}

.theme-modern-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ec4899 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.theme-modern-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 50%, #db2777 100%);
}

.theme-modern-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.theme-modern-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.theme-modern-hero-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
  min-height: 100vh;
  justify-content: center;
  padding: 6rem 0;
}

@media (min-width: 1024px) {
  .theme-modern-hero-layout {
    flex-direction: row;
    gap: 6rem;
    padding: 8rem 0;
  }
}

.theme-modern-hero-content {
  text-align: center;
  max-width: 700px;
  flex: 1;
}

@media (min-width: 1024px) {
  .theme-modern-hero-content {
    text-align: left;
  }
}

.theme-modern-hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  align-items: center;
  margin-top: 1rem;
}

@media (min-width: 640px) {
  .theme-modern-hero-actions {
    flex-direction: row;
    justify-content: center;
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-actions {
    justify-content: flex-start;
  }
}

.theme-modern-hero-action-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.theme-modern-hero-upload-btn {
  cursor: pointer;
}

.theme-modern-hero-upload-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.theme-modern-spinner {
  animation: spin 1s linear infinite;
}

/* Hero Image Styles */
.theme-modern-hero-image-container {
  position: relative;
  flex-shrink: 0;
  order: -1;
}

@media (min-width: 1024px) {
  .theme-modern-hero-image-container {
    order: 0;
  }
}

.theme-modern-hero-image-wrapper {
  position: relative;
  width: 18rem;
  height: 18rem;
}

@media (min-width: 640px) {
  .theme-modern-hero-image-wrapper {
    width: 20rem;
    height: 20rem;
  }
}

@media (min-width: 1024px) {
  .theme-modern-hero-image-wrapper {
    width: 24rem;
    height: 24rem;
  }
}

.theme-modern-hero-image-wrapper::before {
  content: '';
  position: absolute;
  inset: -0.5rem;
  background: linear-gradient(45deg, #60a5fa, #a78bfa, #f472b6, #06b6d4, #60a5fa);
  border-radius: 50%;
  filter: blur(1.5rem);
  opacity: 0.7;
  animation: rotate 8s linear infinite;
  background-size: 200% 200%;
}

.theme-modern-hero-image-wrapper:hover::before {
  opacity: 1;
  filter: blur(2rem);
}

.theme-modern-hero-image-wrapper::after {
  content: '';
  position: absolute;
  inset: 0.75rem;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  border-radius: 50%;
  z-index: 1;
  border: 2px solid rgba(96, 165, 250, 0.2);
}

.theme-modern-hero-image {
  position: absolute;
  inset: 1.25rem;
  width: calc(100% - 2.5rem);
  height: calc(100% - 2.5rem);
  border-radius: 50%;
  object-fit: cover;
  z-index: 10;
  border: 3px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.theme-modern-hero-image-wrapper:hover .theme-modern-hero-image {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.2);
}

.theme-modern-hero-upload-overlay {
  position: absolute;
  inset: 1.25rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  opacity: 0;
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 20;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 2px solid rgba(96, 165, 250, 0.3);
}

.theme-modern-hero-image-wrapper:hover .theme-modern-hero-upload-overlay {
  opacity: 1;
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(96, 165, 250, 0.5);
}

.theme-modern-hero-upload-icon {
  width: 2.5rem;
  height: 2.5rem;
  margin-bottom: 0.75rem;
  color: #60a5fa;
}

.theme-modern-hero-upload-loading {
  position: absolute;
  inset: 1.25rem;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 30;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 2px solid rgba(96, 165, 250, 0.3);
}

.theme-modern-hero-upload-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  color: white;
}

.theme-modern-hero-upload-spinner {
  width: 4rem;
  height: 4rem;
  color: #60a5fa;
  animation: spin 1s linear infinite;
}

.theme-modern-hero-upload-text {
  font-size: 1.125rem;
  font-weight: 600;
  background: linear-gradient(90deg, #60a5fa, #a78bfa, #f472b6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .theme-modern-hero-container {
    padding: 0 1rem;
  }

  .theme-modern-hero-layout {
    gap: 3rem;
    padding: 5rem 0;
  }

  .theme-modern-hero-title {
    font-size: 2.5rem;
  }

  .theme-modern-hero-subtitle {
    font-size: 1.125rem;
  }

  .theme-modern-hero-image-wrapper {
    width: 16rem;
    height: 16rem;
  }
}
