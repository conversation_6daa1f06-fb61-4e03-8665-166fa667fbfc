/* Modern Theme - Navbar Component */

.theme-modern-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.theme-modern-navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.theme-modern-navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  text-decoration: none;
  transition: color 0.3s ease;
}

.theme-modern-navbar-brand:hover {
  color: #3b82f6;
}

.theme-modern-navbar-nav {
  display: none;
  gap: 2rem;
}

@media (min-width: 768px) {
  .theme-modern-navbar-nav {
    display: flex;
  }
}

.theme-modern-navbar-link {
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.theme-modern-navbar-link:hover {
  color: #1f2937;
}

.theme-modern-navbar-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.theme-modern-navbar-link:hover::after {
  width: 100%;
}

.theme-modern-navbar-mobile-toggle {
  display: block;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: #1f2937;
}

.theme-modern-navbar-mobile-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: #1f2937;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.theme-modern-navbar-mobile-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.theme-modern-navbar-mobile-icon {
  width: 1.5rem;
  height: 1.5rem;
}

@media (min-width: 768px) {
  .theme-modern-navbar-mobile-toggle {
    display: none;
  }
}

/* Mobile menu */
.theme-modern-navbar-mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.theme-modern-navbar-mobile-menu.active {
  display: block;
}

.theme-modern-navbar-mobile-nav {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
}

.theme-modern-navbar-mobile-link {
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
}

.theme-modern-navbar-mobile-link:hover {
  color: #1f2937;
}

/* Focus styles for navbar links */
.theme-modern-navbar-link:focus,
.theme-modern-navbar-mobile-link:focus,
.theme-modern-navbar-brand:focus {
  outline: none;
  color: #3b82f6;
}

.theme-modern-navbar-link:focus::after {
  width: 100%;
}
