"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";
import { PortfolioData } from "@/lib/types";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useEffect } from "react";

// Helper function to strip HTML tags - consistent server/client rendering
const stripHtml = (html: string): string => {
    if (!html) return '';

    // Use regex to strip HTML tags - works consistently on server and client
    return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&')  // Replace HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/\s+/g, ' ')    // Normalize whitespace
        .trim();
};

export function ModernAbout({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    // Clean existing HTML data on component mount
    useEffect(() => {
        if (isEditing && dispatch && data.bio) {
            const cleanBio = stripHtml(data.bio);
            if (cleanBio !== data.bio) {
                dispatch({ type: 'UPDATE_FIELD', payload: { field: 'bio', value: cleanBio } });
            }
        }
    }, [isEditing, dispatch, data.bio]);

    const handleUpdate = (field: keyof Omit<PortfolioData, 'projects'>, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_FIELD', payload: { field, value } });
        }
    };

    return (
        <section id="about" className="theme-modern-about">
            <div className="theme-modern-about-container">
                <div className="theme-modern-about-content">
                    <div className="theme-modern-about-header">
                        <EditableText
                            isEditing={isEditing}
                            tagName="h2"
                            className={`theme-modern-about-title ${isEditing ? 'editable-field-inline editable-field-modern' : ''}`}
                            initialValue="About Me"
                            placeholder="Section Title"
                            onSave={() => {}} // Static title
                        />
                        <div className="theme-modern-about-divider"></div>
                    </div>
                    <div className="theme-modern-about-body">
                        <EditableText
                            isEditing={isEditing}
                            tagName="p"
                            className={`theme-modern-about-text ${isEditing ? 'editable-field-large editable-field-modern' : ''}`}
                            initialValue={data.bio || "Tell your story here. Share your background, experience, and what drives you in your professional journey. Describe your passion, skills, and what makes you unique in your field."}
                            placeholder="Tell your story here. Share your background, experience, and what drives you."
                            onSave={(value) => handleUpdate('bio', value)}
                        />
                    </div>
                </div>
                <div className="theme-modern-about-image-container">
                    <div className="theme-modern-about-image-wrapper">
                        <PortfolioImage
                            isEditing={isEditing}
                            src={data.profileImageUrl || 'https://placehold.co/400x400/1e293b/60a5fa?text=About+Image'}
                            alt={`${data.userName} - About`}
                            width={400}
                            height={400}
                            className="theme-modern-about-image"
                        />
                    </div>
                </div>
            </div>
        </section>
    );
};
